const express = require('express');
const authMiddleware = require('../middleware/authMiddleware');
const validate = require('../middleware/validationMiddleware');
const { updateProviderDetailsSchema } = require('../validation/schemas');
const {
	getAllUsers,
	setUserAsAdmin,
	updateToAdmin,
	getUserById,
	updateProviderDetails,
	updateUserRating,
} = require('../controllers/userController');
const { getAdmins } = require('../controllers/projectController');

const router = express.Router();

// Apply authMiddleware to all routes
// router.use(authMiddleware);

// Get all users (admin only)
router.get('/all', authMiddleware, getAllUsers);

router.get('/admins', getAdmins);

// Set user as admin (admin only)
router.patch('/:userId/set-admin', authMiddleware, setUserAsAdmin);

// Upgrade user to admin
router.get('/:id', authMiddleware, getUserById);

//Update profile
router.put('/update-profile', authMiddleware, updateProviderDetails);
router.put('/:userId/rate', authMiddleware, updateUserRating);

module.exports = router;
