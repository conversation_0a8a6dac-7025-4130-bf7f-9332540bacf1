const express = require('express');
const {
	uploadProject,
	getProjects,
	deleteProject,
	updateProject,
	showSpecAdmin,
	acceptDoc,
	getProjectsByStudentId,
	deleteProjectById, // Import the acceptDoc function
} = require('../controllers/projectController');
const authMiddleware = require('../middleware/authMiddleware');
const validate = require('../middleware/validationMiddleware');
const {
	uploadProjectSchema,
	updateProjectSchema,
} = require('../validation/schemas');
const router = express.Router();
const multer = require('multer');
const upload = multer({ storage: multer.memoryStorage() }); // Stores file in memory

// Route for uploading a project
router.post(
	'/upload',
	authMiddleware,
	upload.single('file'), // Middleware to process file upload
	validate(uploadProjectSchema),
	uploadProject,
);

// Route for getting all projects
router.get('/all', authMiddleware, getProjects);

// Route for deleting a project
router.delete('/:id', authMiddleware, deleteProject);

// Route for updating a project
router.put(
	'/:id',
	authMiddleware,
	validate(updateProjectSchema),
	updateProject,
);

// Route for getting projects assigned to a specific admin
router.get('/assigned/:adminId', authMiddleware, showSpecAdmin);

// Route for accepting a project document
router.put('/accept/:projectId', authMiddleware, acceptDoc);

// Route for getting projects by student ID
router.get('/student/:studentId', authMiddleware, getProjectsByStudentId);

// Route for deleting a project by ID
router.delete('/delete/:id', authMiddleware, deleteProjectById);

module.exports = router;
