const nodemailer = require('nodemailer');

module.exports = async (email, subject, text) => {
	try {
		const transporter = nodemailer.createTransport({
			service: 'gmail',
			host: 'smtp.gmail.com',
			port: 465,
			secure: true, // use SSL
			auth: {
				user: process.env.EMAIL_USER,
				pass: process.env.EMAIL_PASS,
			},
		});

		// Verify connection configuration
		await transporter.verify();
		console.log('SMTP connection verified');

		const mailOptions = {
			from: `"Upload Docs" <${process.env.EMAIL_USER}>`,
			to: email,
			subject: subject,
			html: `
        <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; border: 1px solid #e0e0e0; border-radius: 12px; overflow: hidden;">
    <!-- Header Section -->
    <div style="text-align: center; background-color: #0078D7; padding: 30px 20px; color: white;">
        <h1 style="margin: 0; font-size: 28px; font-weight: bold;">Upload Doc</h1>
        <p style="margin: 10px 0 0; font-size: 16px; opacity: 0.9;">Your document management solution</p>
    </div>

    <!-- Body Section -->
    <div style="padding: 30px 20px; background-color: #ffffff;">
        <p style="font-size: 16px; margin: 0 0 20px;">Dear User,</p>
        <p style="font-size: 16px; margin: 0 0 20px;">${text}</p>

        <p style="font-size: 16px; margin: 0 0 20px;">
            If you have any questions, feel free to reach out to our support team at 
            <a href="mailto:<EMAIL>" style="color: #0078D7; text-decoration: none;"><EMAIL></a>.
        </p>

        <p style="font-size: 16px; margin: 0;">Best regards,</p>
        <p style="font-size: 16px; margin: 0; font-weight: bold;">Daniel Agbeni</p>
    </div>

    <!-- Footer Section -->
    <div style="padding: 20px; background-color: #f9f9f9; text-align: center;">
        <hr style="border: none; border-top: 1px solid #e0e0e0; margin: 0 0 20px;" />
        <p style="font-size: 12px; color: #666; margin: 0;">
            Please do not reply to this email. This mailbox is not monitored.<br />
            &copy; ${new Date().getFullYear()} Daniel Agbeni. All rights reserved.
        </p>
    </div>
</div>
    `,
			priority: 'high',
		};

		const info = await transporter.sendMail(mailOptions);
		// console.log('Email sent successfully:', info.messageId);
		return info;
	} catch (error) {
		console.error('Email sending failed:', {
			errorCode: error.code,
			errorMessage: error.message,
			response: error.response,
		});
		throw error;
	}
};
