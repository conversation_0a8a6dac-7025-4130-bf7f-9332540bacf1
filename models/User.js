const mongoose = require('mongoose');

const UserSchema = new mongoose.Schema(
	{
		name: { type: String, required: true },
		email: { type: String, required: true, unique: true },
		googleId: { type: String, default: null },
		profilePicture: { type: String, default: null },
		matricNumber: { type: String, required: true, unique: true },
		password: { type: String, required: true },
		isAdmin: { type: Boolean, default: false },
		superAdmin: { type: Boolean, default: false },
		isVerified: { type: Boolean, default: false },
		documentsReceived: { type: Number, default: 1 },
		documentToken: { type: Number, default: 0 },

		// Provider details (only for admins)
		openingHours: { type: String, default: null },
		printingCost: { type: Number, default: null },
		printingLocation: { type: String, default: null },
		additionalInfo: { type: String, default: null },

		discountRates: {
			type: [
				{
					minPages: { type: Number, required: true },
					maxPages: { type: Number, required: true },
					discount: { type: Number, required: true }, // Percentage discount
				},
			],
			default: [],
		},

		rating: { type: Number, default: 0, min: 0, max: 5 },

		reviews: {
			type: [
				{
					userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
					name: { type: String, default: 'Anonymous' },
					rating: { type: Number, required: true },
					comment: { type: String, default: '' },
				},
			],
			default: [],
		},

		adminStatus: {
			type: String,
			enum: ['active', 'inactive', 'suspended'],
			default: 'active',
		},
		queueTimeEstimate: { type: Number, default: 0 },
		supportContact: { type: String, default: null },
	},
	{ timestamps: true },
);

// Method to check if profile is complete for admins
UserSchema.methods.isProfileComplete = function () {
	if (this.isAdmin) {
		return (
			this.openingHours && this.printingCost !== null && this.printingLocation
		);
	}
	return true;
};

module.exports = mongoose.model('User', UserSchema);
