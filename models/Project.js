const mongoose = require('mongoose');

const ProjectSchema = new mongoose.Schema(
	{
		studentId: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
			required: true,
		},
		studentName: {
			type: String,
			required: true,
		},
		matricNumber: {
			type: String,
			required: true,
		},
		fileUrl: {
			type: String,
			required: true,
		},
		title: {
			type: String,
			required: true,
		},
		assignedAdmin: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'User',
		},
		status: {
			type: String,
			enum: ['pending', 'accepted', 'rejected'],
			default: 'pending',
		},
		fileType: {
			type: String,
			required: true,
			enum: [
				'application/pdf',
				'application/msword',
				'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
				'text/plain',
				'application/vnd.ms-excel',
				'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
				'image/jpeg',
				'image/png',
				'image/gif',
			],
		},
		pageCount: {
			type: Number,
			default: 0,
		},
		price: {
			type: Number,
		},
		originalPrice: {
			type: Number,
		},
		discountPercentage: {
			type: Number,
			default: 0,
		},
		// publicId: { type: String, required: true }, // Commented out as in original
	},
	{ timestamps: true },
);

const Project = mongoose.model('Project', ProjectSchema);
module.exports = Project;
