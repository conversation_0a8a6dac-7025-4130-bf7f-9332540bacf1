const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth2').Strategy;
const User = require('../models/User');
const bcrypt = require('bcryptjs'); // Needed for password hashing if generating a default password
require('dotenv').config();

module.exports = (passport) => {
	passport.use(
		new GoogleStrategy(
			{
				clientID: process.env.GOOGLE_CLIENT_ID,
				clientSecret: process.env.GOOGLE_CLIENT_SECRET,
				callbackURL: `${process.env.BASE_URL}/api/auth/google/callback`, // Your callback URL
				passReqToCallback: true,
			},
			async (request, accessToken, refreshToken, profile, done) => {
				try {
					// **Important: Check if email already exists**
					let existingUserWithEmail = await User.findOne({
						email: profile.emails[0].value,
					});

					if (existingUserWithEmail) {
						// Email already in use.  Google ID takes precedence
						if (existingUserWithEmail.googleId) {
							return done(null, existingUserWithEmail);
						}

						existingUserWithEmail.googleId = profile.id; //add google Id to existing user
						await existingUserWithEmail.save();
						return done(null, existingUserWithEmail); //return user with google Id added

						//return done(null, false, { message: 'Email already in use.' }); // Or handle this how you want.  e.g., redirect to a "link accounts" page.
					}

					// Check if user already exists with this Google ID
					let user = await User.findOne({ googleId: profile.id });

					if (user) {
						// User exists, return the user
						return done(null, user);
					} else {
						// User doesn't exist, create a new user
						const randomPassword = Math.random().toString(36).slice(-8); // Generate a random string
						const hashedPassword = await bcrypt.hash(randomPassword, 10); // Hash the password
						const randomId = Math.random().toString(36).slice(-8); // Generate a random string

						user = await User.create({
							googleId: profile.id,
							name: profile.displayName,
							email: profile.emails[0].value, // Get the primary email
							isVerified: true, // Assuming Google verified the email
							password: hashedPassword, // Use the hashed random password
							matricNumber: randomId, // Or generate a unique one if needed.  Consider storing googleId as matricNumber.
							// profilePicture: profile.photos[0].value //Access profile picture -  Store the profile picture URL
						});

						return done(null, user);
					}
				} catch (err) {
					console.error(err);
					return done(err, false);
				}
			},
		),
	);

	passport.serializeUser((user, done) => {
		done(null, user.id);
	});

	passport.deserializeUser(async (id, done) => {
		try {
			const user = await User.findById(id);
			done(null, user);
		} catch (err) {
			done(err, null);
		}
	});
};
