const { z } = require('zod');

// User validation schemas
const registerUserSchema = z.object({
	name: z.string().min(2, { message: 'Name must be at least 2 characters' }),
	email: z.string().email({ message: 'Invalid email address' }),
	matricNumber: z
		.string()
		.min(5, { message: 'Matric number must be at least 5 characters' }),
	password: z
		.string()
		.min(6, { message: 'Password must be at least 6 characters' }),
});

const loginUserSchema = z.object({
	email: z.string().email({ message: 'Invalid email address' }),
	password: z.string().min(1, { message: 'Password is required' }),
});

const verifyEmailSchema = z.object({
	email: z.string().email({ message: 'Invalid email address' }),
	otp: z.string().min(4, { message: 'OTP is required' }),
});

const forgotPasswordSchema = z.object({
	email: z.string().email({ message: 'Invalid email address' }),
});

const resetPasswordSchema = z.object({
	token: z.string().min(1, { message: 'Token is required' }),
	password: z
		.string()
		.min(6, { message: 'Password must be at least 6 characters' }),
});

// Project validation schemas
const uploadProjectSchema = z.object({
	title: z.string().min(3, { message: 'Title must be at least 3 characters' }),
	assignedAdmin: z.string().min(1, { message: 'Assigned admin is required' }),
});

const updateProjectSchema = z.object({
	studentName: z.string().optional(),
	matricNumber: z.string().optional(),
});

// Payment validation schemas
const initializePaymentSchema = z.object({
	packageId: z.string().min(1, { message: 'Package ID is required' }),
	amount: z.number().positive({ message: 'Amount must be positive' }),
});

// User update schemas
const updateProviderDetailsSchema = z.object({
	openingHours: z.string().optional(),
	printingCost: z.number().optional(),
	printingLocation: z.string().optional(),
	discountRates: z.string().optional(),
	queueTimeEstimate: z.string().optional(),
	supportContact: z.string().optional(),
	additionalInfo: z.string().optional(),
});

// Web Push Notification schemas
const subscriptionSchema = z.object({
	endpoint: z
		.string()
		.url({ message: 'Valid subscription endpoint URL is required' }),
	keys: z.object({
		p256dh: z.string().min(1, { message: 'P256DH key is required' }),
		auth: z.string().min(1, { message: 'Auth key is required' }),
	}),
	expirationTime: z.number().nullable().optional(),
});

const pushSubscriptionSchema = z.object({
	uid: z.string().min(1, { message: 'User ID is required' }),
	subscription: subscriptionSchema,
});

const sendNotificationSchema = z.object({
	recipientUid: z.string().min(1, { message: 'Recipient user ID is required' }),
	senderName: z.string().min(1, { message: 'Sender name is required' }),
	messageContent: z.string().min(1, { message: 'Message content is required' }),
});

const unsubscribeSchema = z.object({
	uid: z.string().min(1, { message: 'User ID is required' }),
	endpoint: z
		.string()
		.url({ message: 'Valid subscription endpoint URL is required' }),
});

module.exports = {
	registerUserSchema,
	loginUserSchema,
	verifyEmailSchema,
	forgotPasswordSchema,
	resetPasswordSchema,
	uploadProjectSchema,
	updateProjectSchema,
	initializePaymentSchema,
	updateProviderDetailsSchema,
	pushSubscriptionSchema,
	sendNotificationSchema,
	unsubscribeSchema,
	subscriptionSchema,
};
