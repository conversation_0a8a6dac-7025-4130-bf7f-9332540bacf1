{"name": "uploaddoc", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node ./server.js", "dev": "nodemon ./server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.7.9", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.3", "connect-mongo": "^5.1.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "helmet": "^8.1.0", "imagekit": "^6.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.5", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "nodemailer": "^6.10.0", "nodemon": "^3.1.9", "passport": "^0.7.0", "passport-google-oauth2": "^0.2.0", "path": "^0.12.7", "promisify": "^0.0.3", "web-push": "^3.6.7", "ws": "^8.18.0", "zod": "^3.24.4"}}