const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const session = require('express-session'); // Import express-session
const passport = require('passport'); // Import passport
const helmet = require('helmet'); // Import helmet for securing HTTP headers
const mongoSanitize = require('express-mongo-sanitize'); // Import express-mongo-sanitize
const connectDB = require('./config/db');
const MongoStore = require('connect-mongo');

dotenv.config();
connectDB();
const app = express();

// Configure express-session
app.use(
	session({
		secret: process.env.SESSION_SECRET,
		resave: false,
		saveUninitialized: false,
		store: MongoStore.create({
			mongoUrl: process.env.MONGO_URI, // same URI used in connectDB
			collectionName: 'sessions',
		}),
		cookie: {
			maxAge: 28 * 24 * 60 * 60 * 1000,
			httpOnly: true,
			secure: process.env.NODE_ENV === 'production',
		},
	}),
);

// Configure Passport
require('./config/passport')(passport); // Pass passport to the config
app.use(passport.initialize());
app.use(passport.session());

// Middleware
app.use(express.json());
app.use(
	cors({
		origin: [
			'http://localhost:3000',
			'https://uploaddoc.vercel.app',
			'https://uploaddoc.vercel.app/auth/login',
			'http://***************:3000',
			// 'capacitor://localhost', // Android WebView
			// 'http://localhost', // Might be needed for Android
		],
		credentials: true,
	}),
);

// Security middleware
// Apply Helmet for securing HTTP headers
app.use(
	helmet({
		contentSecurityPolicy: {
			directives: {
				defaultSrc: ["'self'"],
				scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
				styleSrc: ["'self'", "'unsafe-inline'", 'https:'],
				imgSrc: ["'self'", 'data:', 'https:'],
				connectSrc: ["'self'", 'https:'],
				fontSrc: ["'self'", 'https:'],
				objectSrc: ["'none'"],
				mediaSrc: ["'self'"],
				frameSrc: ["'self'"],
			},
		},
		// Disable X-Powered-By header to hide Express
		hidePoweredBy: true,
		// Configure Cross-Origin options
		crossOriginEmbedderPolicy: false,
		crossOriginOpenerPolicy: { policy: 'same-origin-allow-popups' },
		crossOriginResourcePolicy: { policy: 'cross-origin' },
	}),
);

// Apply express-mongo-sanitize to prevent MongoDB Injection attacks
app.use(
	mongoSanitize({
		replaceWith: '_',
		allowDots: true,
	}),
);

// Routes
const authRoutes = require('./routes/authRoutes');
const userRoutes = require('./routes/userRoutes');
const projectRoutes = require('./routes/projectRoutes');
const notificationRoutes = require('./routes/notificationRoutes');
const paymentRoutes = require('./routes/paymentRoutes');

// Apply routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/projects', projectRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/payment', paymentRoutes);

const PORT = process.env.PORT || 5000;
app.listen(PORT, () =>
	console.log(`Auth Service running on port http://localhost:${PORT}`),
);
