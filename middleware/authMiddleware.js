const jwt = require('jsonwebtoken');
const Token = require('../models/Token');

const authMiddleware = async (req, res, next) => {
	try {
		let token = req.header('Authorization');

		if (!token) {
			return res
				.status(401)
				.json({ message: 'Unauthorized: No token provided' });
		}

		// Extract token if it has the "Bearer " prefix
		if (token.startsWith('Bearer ')) {
			token = token.slice(7, token.length); // Remove "Bearer " prefix
		}

		// Verify if the token exists in the database
		const storedToken = await Token.findOne({ token, isValid: true });
		if (!storedToken) {
			return res
				.status(401)
				.json({ message: 'Unauthorized: Token not found or invalid' });
		}

		// Verify JWT
		const decoded = jwt.verify(token, process.env.JWT_SECRET);
		req.user = decoded;

		// Update last used timestamp
		await Token.updateOne({ _id: storedToken._id }, { lastUsed: new Date() });

		next();
	} catch (error) {
		console.error('Auth Middleware Error:', error);
		res.status(401).json({ message: 'Invalid Token' });
	}
};

module.exports = authMiddleware;
