/**
 * Middleware for validating request data using Zod schemas
 */
const validate = (schema) => (req, res, next) => {
  try {
    // Validate request body against the provided schema
    schema.parse(req.body);
    next();
  } catch (error) {
    // If validation fails, format the errors and return them
    if (error.errors) {
      const formattedErrors = error.errors.map(err => ({
        path: err.path.join('.'),
        message: err.message
      }));
      
      return res.status(400).json({
        message: 'Validation failed',
        errors: formattedErrors
      });
    }
    
    // For any other errors
    return res.status(400).json({
      message: 'Invalid input data',
      error: error.message
    });
  }
};

module.exports = validate;
