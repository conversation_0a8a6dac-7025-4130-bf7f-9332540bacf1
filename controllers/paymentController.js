const axios = require('axios');
const User = require('../models/User');

const PAYSTACK_SECRET = process.env.PAYSTACK_SECRET;
const FRONTEND_URL = process.env.FRONTEND_URL;

const getTokensByPackageId = (packageId) => {
	const tokenMapping = {
		1: 100, // Basic free package = 100 tokens
		2: 250, // ₦2000 = 110 tokens (updated to 250)
		3: 700, // ₦5000 = 300 tokens (updated to 700)
		4: 1500, // ₦10000 = 700 tokens (updated to 1500)
	};
	return tokenMapping[packageId] || 0; // Default to 0 if packageId is invalid
};

const initializePayment = async (req, res) => {
	try {
		const { userId, amount, package } = req.body;

		const packageId = Number(package);
		if (isNaN(packageId)) {
			return res.status(400).json({ error: 'Invalid package format' });
		}

		const user = await User.findById(userId);
		if (!user) {
			return res.status(404).json({ error: 'User not found' });
		}

		// Prevent admins from purchasing the "Basic" token again
		if (user.isAdmin && packageId === 1) {
			return res
				.status(400)
				.json({ error: 'Admins cannot purchase the Basic package again.' });
		}

		// Special case for basic package (free)
		if (packageId === 1 && amount === 0) {
			// Update user without going through payment flow
			const tokensToAdd = getTokensByPackageId(packageId);
			user.documentToken += tokensToAdd;
			user.documentsReceived = 1;
			user.isAdmin = true;
			await user.save();

			return res.json({
				status: 'success',
				data: {
					message: 'Basic package activated successfully',
					tokens: tokensToAdd,
					redirectUrl: `${FRONTEND_URL}/payment-status?status=success&tokens=${tokensToAdd}&amount=0`,
				},
			});
		}

		// Proceed with payment for paid packages
		const response = await axios.post(
			'https://api.paystack.co/transaction/initialize',
			{
				email: user.email,
				amount: amount * 100,
				currency: 'NGN',
				callback_url: `${process.env.BASE_URL}/api/payment/verify`,
				metadata: { userId, package: packageId },
			},
			{
				headers: {
					Authorization: `Bearer ${PAYSTACK_SECRET}`,
					'Content-Type': 'application/json',
				},
			},
		);

		res.json({ status: 'success', data: response.data });
	} catch (error) {
		console.error(
			'Paystack Initialization Error:',
			error.response?.data || error.message,
		);
		res.status(500).json({ error: 'Payment initialization failed' });
	}
};

const verifyPayment = async (req, res) => {
	try {
		const { reference } = req.query;

		const response = await axios.get(
			`https://api.paystack.co/transaction/verify/${reference}`,
			{ headers: { Authorization: `Bearer ${PAYSTACK_SECRET}` } },
		);

		const paymentData = response.data.data;

		if (paymentData.status !== 'success') {
			return res.redirect(
				`${FRONTEND_URL}/payment-status?status=failed&message=${encodeURIComponent(
					'Payment failed or incomplete',
				)}`,
			);
		}

		const userId = paymentData.metadata.userId;
		const packageId = Number(paymentData.metadata.package);
		if (isNaN(packageId)) {
			return res.redirect(
				`${FRONTEND_URL}/payment-status?status=failed&message=${encodeURIComponent(
					'Invalid package format',
				)}`,
			);
		}

		const user = await User.findById(userId);
		if (!user) {
			return res.redirect(
				`${FRONTEND_URL}/payment-status?status=failed&message=${encodeURIComponent(
					'User not found',
				)}`,
			);
		}

		const tokensToAdd = getTokensByPackageId(packageId);
		if (tokensToAdd === 0) {
			return res.redirect(
				`${FRONTEND_URL}/payment-status?status=failed&message=${encodeURIComponent(
					'Invalid package selected',
				)}`,
			);
		}

		user.documentToken += tokensToAdd;
		user.documentsReceived = 1;
		user.isAdmin = true;
		await user.save();

		const successUrl = `${FRONTEND_URL}/payment-status?status=success&reference=${reference}&tokens=${tokensToAdd}&amount=${
			paymentData.amount / 100
		}`;

		res.redirect(successUrl);
	} catch (error) {
		console.error('Paystack Verification Error:', error.message);
		res.redirect(
			`${FRONTEND_URL}/payment-status?status=error&message=${encodeURIComponent(
				error.message,
			)}`,
		);
	}
};

module.exports = { initializePayment, verifyPayment };
