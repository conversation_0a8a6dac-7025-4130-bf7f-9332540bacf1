const Project = require('../models/Project');
const User = require('../models/User');
const mongoose = require('mongoose');
const imagekit = require('../config/imagekit');
const path = require('path');

const uploadProject = async (req, res) => {
	try {
		if (!req.user) {
			return res.status(401).json({ message: 'Authentication required' });
		}

		const {
			title,
			assignedAdmin,
			price,
			originalPrice,
			discountPercentage,
			pageCount,
		} = req.body;

		if (!title || !req.file) {
			return res.status(400).json({ message: 'All fields are required' });
		}

		const allowedMimeTypes = [
			'application/pdf',
			'application/msword',
			'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
			'text/plain',
			'application/vnd.ms-excel',
			'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			'image/jpeg',
			'image/png',
		];

		const allowedExtensions = [
			'.pdf',
			'.doc',
			'.docx',
			'.txt',
			'.xls',
			'.xlsx',
			'.jpg',
			'.jpeg',
			'.png',
		];

		const fileExtension = path.extname(req.file.originalname).toLowerCase();

		if (
			!allowedMimeTypes.includes(req.file.mimetype) ||
			!allowedExtensions.includes(fileExtension)
		) {
			return res.status(400).json({
				message:
					'Invalid file type. Allowed types: PDF, DOC, DOCX, TXT, XLSX, JPG, PNG',
			});
		}

		const admin = await User.findById(assignedAdmin);
		if (!admin || !admin.isAdmin) {
			return res.status(400).json({ message: 'Invalid admin selection' });
		}

		const student = await User.findById(req.user.id);
		if (!student) {
			return res.status(404).json({ message: 'Student not found' });
		}

		// Convert file buffer to base64
		const fileBuffer = req.file.buffer.toString('base64');

		// Upload to ImageKit
		const uploadResult = await imagekit.upload({
			file: fileBuffer,
			fileName: req.file.originalname,
			folder: 'student_projects',
		});

		const projectData = {
			title,
			studentId: req.user.id,
			studentName: student.name,
			matricNumber: student.matricNumber,
			fileUrl: uploadResult.url,
			publicId: uploadResult.fileId,
			assignedAdmin,
			fileType: req.file.mimetype,
		};

		// Optional fields
		if (price) projectData.price = price;
		if (originalPrice) projectData.originalPrice = originalPrice;
		if (discountPercentage) projectData.discountPercentage = discountPercentage;
		if (pageCount) projectData.pageCount = pageCount;

		const project = await Project.create(projectData);

		res.status(201).json({
			message: 'Project submitted successfully',
			project,
		});
	} catch (error) {
		console.error(error);
		res.status(500).json({ message: 'Server Error' });
	}
};
// Get All Projects (Admins Only) with Pagination
const getProjects = async (req, res) => {
	try {
		// Parse pagination parameters from query string
		const page = parseInt(req.query.page) || 1; // Default to page 1
		const limit = parseInt(req.query.limit) || 10; // Default to 10 items per page
		const skip = (page - 1) * limit;

		// Get total count for pagination metadata
		const totalCount = await Project.countDocuments({});

		// Fetch projects with pagination
		const projects = await Project.find({})
			.sort({ createdAt: -1 }) // Sort by newest first
			.skip(skip)
			.limit(limit);

		// Send response with pagination metadata
		res.status(200).json({
			projects,
			pagination: {
				totalCount,
				totalPages: Math.ceil(totalCount / limit),
				currentPage: page,
				limit,
			},
		});
	} catch (error) {
		console.error('Error fetching projects:', error);
		res.status(500).json({ message: 'Server Error' });
	}
};

// Delete Project
const deleteProject = async (req, res) => {
	try {
		const project = await Project.findById(req.params.id);
		if (!project) {
			return res.status(404).json({ message: 'Project not found' });
		}

		await project.deleteOne();

		res.json({ message: 'Project deleted successfully' });
	} catch (error) {
		console.error('Delete Project Error:', error);
		res.status(500).json({ message: 'Server Error' });
	}
};

const getAdmins = async (req, res) => {
	try {
		// Parse pagination parameters from query string
		const page = parseInt(req.query.page) || 1; // Default to page 1
		const limit = parseInt(req.query.limit) || 10; // Default to 10 items per page
		const skip = (page - 1) * limit;

		// Get total count of admins for pagination metadata
		const totalCount = await User.countDocuments({ isAdmin: true });

		// Fetch admins with pagination and exclude the password field
		const allAdmins = await User.find({ isAdmin: true })
			.select('-password')
			.sort({ createdAt: -1 }) // Sort by newest first
			.skip(skip)
			.limit(limit);

		const admins = allAdmins.map((user) => ({
			_id: user._id,
			name: user.name,
			email: user.email,
			matricNumber: user.matricNumber,
			isAdmin: user.isAdmin,
			superAdmin: user.superAdmin,
			isVerified: user.isVerified,
			documentsReceived: user.documentsReceived,
			documentToken: user.documentToken,
			openingHours: user.openingHours,
			printingCost: user.printingCost,
			printingLocation: user.printingLocation,
			discountRates: user.discountRates,
			rating: user.rating,
			adminStatus: user.adminStatus,
			queueTimeEstimate: user.queueTimeEstimate,
			supportContact: user.supportContact,
			additionalInfo: user.additionalInfo,
			reviews: user.reviews,
			profilePicture: user.profilePicture,
			createdAt: user.createdAt,
			updatedAt: user.updatedAt,
		}));

		// Send response with pagination metadata
		res.json({
			admins,
			pagination: {
				totalCount,
				totalPages: Math.ceil(totalCount / limit),
				currentPage: page,
				limit,
			},
		});
	} catch (error) {
		console.error(error);
		res.status(500).json({ message: 'Server Error' });
	}
};

// Update Project Details (Admins Only)
const updateProject = async (req, res) => {
	try {
		if (!req.user.isAdmin) {
			return res.status(403).json({ message: 'Access Denied' });
		}

		const project = await Project.findById(req.params.id);
		if (!project) {
			return res.status(404).json({ message: 'Project not found' });
		}

		project.studentName = req.body.studentName || project.studentName;
		project.matricNumber = req.body.matricNumber || project.matricNumber;

		await project.save();

		res.json({ message: 'Project updated successfully', project });
	} catch (error) {
		console.error('Update Project Error:', error);
		res.status(500).json({ message: 'Server Error' });
	}
};
const showSupAdmin = async (req, res) => {
	try {
		// Parse pagination parameters from query string
		const page = parseInt(req.query.page) || 1; // Default to page 1
		const limit = parseInt(req.query.limit) || 10; // Default to 10 items per page
		const skip = (page - 1) * limit;

		// Get total count for pagination metadata
		const totalCount = await Project.countDocuments({});

		// Fetch projects with pagination
		const projects = await Project.find({})
			.sort({ createdAt: -1 }) // Sort by newest first
			.skip(skip)
			.limit(limit);

		// Send response with pagination metadata
		res.status(200).json({
			projects,
			pagination: {
				totalCount,
				totalPages: Math.ceil(totalCount / limit),
				currentPage: page,
				limit,
			},
		});
	} catch (error) {
		console.error('Error fetching projects:', error);
		res.status(500).json({ message: 'Server Error' });
	}
};

const showSpecAdmin = async (req, res) => {
	try {
		const { adminId } = req.params;

		// Parse pagination parameters from query string
		const page = parseInt(req.query.page) || 1; // Default to page 1
		const limit = parseInt(req.query.limit) || 10; // Default to 10 items per page
		const skip = (page - 1) * limit;

		// Check if adminId is a valid ObjectId
		if (!mongoose.Types.ObjectId.isValid(adminId)) {
			return res.status(400).json({ message: 'Invalid admin ID' });
		}

		// Get total count for pagination metadata
		const totalCount = await Project.countDocuments({ assignedAdmin: adminId });

		// Fetch projects assigned to the admin with pagination
		const projects = await Project.find({ assignedAdmin: adminId })
			.sort({ createdAt: -1 }) // Sort by newest first
			.skip(skip)
			.limit(limit);

		// Map through the projects and hide fileUrl if not accepted
		const filteredProjects = projects.map((project) => {
			const projectObj = project.toObject();
			if (projectObj.status !== 'accepted') {
				delete projectObj.fileUrl;
			}
			return projectObj;
		});

		// Send response with pagination metadata
		res.status(200).json({
			projects: filteredProjects,
			pagination: {
				totalCount,
				totalPages: Math.ceil(totalCount / limit),
				currentPage: page,
				limit,
			},
		});
	} catch (error) {
		console.error('Error fetching assigned projects:', error);
		res.status(500).json({ message: 'Server Error' });
	}
};

const acceptDoc = async (req, res) => {
	try {
		if (!req.user) {
			return res.status(401).json({ message: 'Authentication required' });
		}
		if (!req.user.isAdmin) {
			return res.status(403).json({ message: 'Access Denied' });
		}

		const { projectId } = req.params;

		// Check if projectId is a valid ObjectId
		if (!mongoose.Types.ObjectId.isValid(projectId)) {
			return res.status(400).json({ message: 'Invalid project ID' });
		}

		// Find the project by ID
		const project = await Project.findById(projectId);
		if (!project) {
			return res.status(404).json({ message: 'Project not found' });
		}

		// Check if the current user is the assigned admin for this project
		if (project.assignedAdmin.toString() !== req.user.id) {
			return res
				.status(403)
				.json({ message: 'You are not the assigned admin for this project' });
		}

		// Fetch the admin user document from the database
		const adminUser = await User.findById(req.user.id);
		if (!adminUser) {
			return res.status(404).json({ message: 'Admin user not found' });
		}

		// Check if the admin has document tokens available
		if (adminUser.documentToken <= 0) {
			return res.status(400).json({
				message: 'Insufficient document tokens to accept this project',
			});
		}

		// Start a database session for transaction
		const session = await mongoose.startSession();
		session.startTransaction();

		try {
			// Update the project status to 'accepted'
			project.status = 'accepted';
			await project.save({ session });

			// Decrement the admin's documentToken
			adminUser.documentToken -= 1;
			await adminUser.save({ session }); // Save the updated admin user document

			// Increment the assigned admin's documentsReceived count
			await User.findByIdAndUpdate(
				project.assignedAdmin,
				{ $inc: { documentsReceived: 1 } },
				{ session },
			);

			// Commit the transaction
			await session.commitTransaction();

			// End the session
			session.endSession();

			// Fetch the updated user document to include updated fields (documentToken and isAdmin)
			const updatedUser = await User.findById(req.user.id);

			// Include the file URL in the response after acceptance
			res.status(200).json({
				message: 'Project accepted successfully',
				project: {
					...project.toObject(),
					fileUrl: project.fileUrl, // Include the file URL here
				},
				user: updatedUser, // Send back the updated user object
			});
		} catch (error) {
			// If any error occurs, abort the transaction
			await session.abortTransaction();
			session.endSession();
			throw error;
		}
	} catch (error) {
		console.error('Error accepting document:', error);
		res.status(500).json({ message: 'Server Error' });
	}
};

// GET projects by student ID with pagination
const getProjectsByStudentId = async (req, res) => {
	try {
		const { studentId } = req.params;

		// Parse pagination parameters from query string
		const page = parseInt(req.query.page) || 1; // Default to page 1
		const limit = parseInt(req.query.limit) || 10; // Default to 10 items per page
		const skip = (page - 1) * limit;

		// Get total count for pagination metadata
		const totalCount = await Project.countDocuments({ studentId: studentId });

		// Fetch projects with pagination
		const projects = await Project.find({ studentId: studentId })
			.sort({ createdAt: -1 }) // Sort by newest first
			.skip(skip)
			.limit(limit);

		// Send response with pagination metadata
		res.json({
			projects,
			pagination: {
				totalCount,
				totalPages: Math.ceil(totalCount / limit),
				currentPage: page,
				limit,
			},
		});
	} catch (error) {
		console.error(error);
		res.status(500).json({ message: 'Error fetching projects' });
	}
};
// DELETE project by ID
const deleteProjectById = async (req, res) => {
	try {
		const { projectId } = req.params;
		const deletedProject = await Project.findByIdAndDelete(projectId);

		if (!deletedProject) {
			return res.status(404).json({ message: 'Project not found' });
		}

		res.json({ message: 'Project deleted successfully' });
	} catch (error) {
		console.error(error);
		res.status(500).json({ message: 'Error deleting project' });
	}
};

module.exports = {
	uploadProject,
	getProjects,
	deleteProject,
	updateProject,
	getAdmins,
	showSpecAdmin,
	showSupAdmin,
	acceptDoc,
	getProjectsByStudentId,
	deleteProjectById,
};
