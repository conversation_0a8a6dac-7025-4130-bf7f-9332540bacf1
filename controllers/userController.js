const { default: mongoose } = require('mongoose');
const User = require('../models/User');

const getAllUsers = async (req, res) => {
	try {
		// Only superAdmin should have access
		// if (!req.user.superAdmin) {
		// 	return res
		// 		.status(403)
		// 		.json({ message: 'Access denied. Super Admin only.' });
		// }

		// Parse pagination parameters from query string
		const page = parseInt(req.query.page) || 1; // Default to page 1
		const limit = parseInt(req.query.limit) || 10; // Default to 10 items per page
		const skip = (page - 1) * limit;

		// Get total count for pagination metadata
		const totalCount = await User.countDocuments({});

		// Fetch users with pagination
		const users = await User.find({})
			.select('-password') // Exclude password from the response
			.sort({ createdAt: -1 }) // Sort by newest first
			.skip(skip)
			.limit(limit);

		// Send response with pagination metadata
		res.json({
			users,
			pagination: {
				totalCount,
				totalPages: Math.ceil(totalCount / limit),
				currentPage: page,
				limit,
			},
		});
	} catch (error) {
		console.error('Get All Users Error:', error);
		res.status(500).json({ message: 'Server Error' });
	}
};

const setUserAsAdmin = async (req, res) => {
	try {
		// Check if the requesting user is admin
		if (!req.user.isAdmin) {
			return res.status(403).json({ message: 'Access denied. Admin only.' });
		}

		const { userId } = req.params;

		// Check if user exists
		const user = await User.findById(userId);
		if (!user) {
			return res.status(404).json({ message: 'User not found' });
		}

		// Don't allow admin to remove their own admin status
		if (userId === req.user.id) {
			return res
				.status(400)
				.json({ message: 'Cannot modify your own admin status' });
		}

		// Toggle admin status
		user.isAdmin
			? (user.documentToken = 0)
			: ((user.documentsReceived = 1), (user.documentToken += 10));
		user.isAdmin = !user.isAdmin;
		await user.save();

		res.json({
			message: `User ${
				user.isAdmin ? 'promoted with 10 tokens to' : 'removed from'
			} admin role successfully`,
			user: {
				id: user._id,
				name: user.name,
				email: user.email,
				isAdmin: user.isAdmin,
			},
		});
	} catch (error) {
		console.error('Set Admin Error:', error);
		res.status(500).json({ message: 'Server Error' });
	}
};

const getUserById = async (req, res) => {
	try {
		let { id } = req.params;

		// If 'me' is passed, get the authenticated user's ID
		if (id === 'me') {
			if (!req.user || !req.user._id) {
				return res
					.status(400)
					.json({ message: 'Invalid request. User ID required.' });
			}
			id = req.user._id; // Get actual ObjectId from authenticated user
		}

		// Validate ObjectId before querying
		if (!mongoose.Types.ObjectId.isValid(id)) {
			return res.status(400).json({ message: 'Invalid user ID' });
		}

		const user = await User.findById(id).select('-password'); // Exclude password field
		if (!user) {
			return res.status(404).json({ message: 'User not found' });
		}

		res.json(user);
	} catch (error) {
		console.error('Error getting user:', error);
		res.status(500).json({ message: 'Server error' });
	}
};

const updateProviderDetails = async (req, res) => {
	try {
		const {
			openingHours,
			printingCost,
			printingLocation,
			discountRates,
			supportContact,
			additionalInfo,
		} = req.body;

		const userId = req.user.id; // Assuming user ID is extracted from auth middleware

		// Find user
		const user = await User.findById(userId);

		if (!user) {
			console.log('User not found');
			return res.status(404).json({ message: 'User not found' });
		}

		// Ensure the user is an admin before allowing updates
		if (!user.isAdmin) {
			return res.status(403).json({
				message: 'Access denied. Only admins can update provider details',
			});
		}

		// Update provider details
		user.openingHours = openingHours ?? user.openingHours;
		user.printingCost = printingCost ?? user.printingCost;
		user.printingLocation = printingLocation ?? user.printingLocation;
		user.supportContact = supportContact ?? user.supportContact;
		user.additionalInfo = additionalInfo ?? user.additionalInfo;

		// Update discount rates only if provided
		if (Array.isArray(discountRates)) {
			user.discountRates = discountRates;
		}

		await user.save();

		res.status(200).json({
			message: 'Provider details updated successfully',
			user,
		});
	} catch (error) {
		res.status(500).json({ message: 'Server error', error: error.message });
	}
};
const updateUserRating = async (req, res) => {
	try {
		const { userId } = req.params; // ID of the admin being rated
		const { rating, comment } = req.body; // Rating (1-5) and optional comment
		const currentUserId = req.user.id; // ID of the user submitting the rating

		// Validation
		if (!mongoose.Types.ObjectId.isValid(userId)) {
			return res.status(400).json({ message: 'Invalid admin ID' });
		}

		if (rating === undefined || rating === null || rating < 1 || rating > 5) {
			return res
				.status(400)
				.json({ message: 'Rating must be between 1 and 5' });
		}

		// Find the admin user
		const admin = await User.findById(userId);

		if (!admin) {
			return res.status(404).json({ message: 'Admin not found' });
		}

		if (!admin.isAdmin) {
			return res
				.status(400)
				.json({ message: 'User is not an admin and cannot be rated' });
		}

		// Check if the user has already reviewed
		const existingReview = admin.reviews.find(
			(review) => review.userId.toString() === currentUserId.toString(),
		);

		if (existingReview) {
			// Update existing review
			existingReview.rating = rating;
			existingReview.comment = comment || existingReview.comment; // Update comment or keep the existing one
		} else {
			// Add new review
			admin.reviews.push({
				userId: currentUserId,
				rating: rating,
				comment: comment,
			});
		}

		// Recalculate average rating
		let totalRating = 0;
		for (const review of admin.reviews) {
			totalRating += review.rating;
		}
		admin.rating =
			admin.reviews.length > 0 ? totalRating / admin.reviews.length : 0;

		await admin.save();

		res.status(200).json({
			message: 'Rating submitted successfully',
			newRating: admin.rating,
		});
	} catch (error) {
		console.error('Error updating rating:', error);
		res.status(500).json({ message: 'Server error', error: error.message });
	}
};

module.exports = {
	getAllUsers,
	setUserAsAdmin,
	getUserById,
	updateProviderDetails,
	updateUserRating, // Export the new function
};
