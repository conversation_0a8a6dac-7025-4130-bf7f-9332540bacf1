const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const { generateOTP, saveOTP, verifyOTP } = require('../utils/OTPservice');
const sendEmail = require('../utils/emailService'); // You'll need to implement this
const Token = require('../models/Token');
const GoogleStrategy = require('passport-google-oauth2').Strategy;
const passport = require('passport');
const dotenv = require('dotenv');
dotenv.config();

const registerUser = async (req, res) => {
	const { name, email, matricNumber, password } = req.body;

	try {
		// Check if user already exists
		const userExists = await User.findOne({ email });
		if (userExists) {
			return res.status(400).json({ message: 'User already exists' });
		}

		// Hash the password
		const hashedPassword = await bcrypt.hash(password, 10);

		// Create the user
		const user = await User.create({
			name,
			email,
			matricNumber,
			password: hashedPassword,
			isVerified: false,
		});

		// Generate and save OTP
		const otp = generateOTP();
		await saveOTP(user._id, otp);

		// Send OTP via email
		await sendEmail(
			email,
			'Email Verification',
			`Your verification code is: ${otp}. This code expires in 5 minutes.`,
		);

		// Schedule account deletion if not verified in 5 minutes
		setTimeout(async () => {
			try {
				const userCheck = await User.findById(user._id);
				if (userCheck && !userCheck.isVerified) {
					await User.deleteOne({ _id: user._id });
					console.log(`User ${user._id} deleted due to unverified email.`);
				}
			} catch (error) {
				console.error('Error during scheduled deletion:', error);
			}
		}, 5 * 60 * 1000);

		// Respond with success message and user data
		res.status(201).json({
			message:
				'User registered successfully. Please check your email for verification code.',
			user: {
				id: user._id, // Ensure this is included
				name: user.name,
				email: user.email,
				matricNumber: user.matricNumber,
			},
		});

		console.log('User registered:', user);
	} catch (error) {
		console.error('Registration error:', error);
		res.status(500).json({ message: 'Server Error' });
	}
};

const verifyEmail = async (req, res) => {
	const { userId, otp } = req.body;

	try {
		await verifyOTP(userId, otp);
		await User.findByIdAndUpdate(userId, { isVerified: true });

		res.json({ message: 'Email verified successfully' });
	} catch (error) {
		res.status(400).json({ message: error.message });
	}
};

const loginUser = async (req, res) => {
	const { email, password } = req.body;

	try {
		const user = await User.findOne({ email });
		if (!user) return res.status(400).json({ message: 'Invalid credentials' });

		if (!user.isVerified) {
			return res.status(400).json({
				message: 'Please verify your email before logging in',
				userId: user._id,
			});
		}

		const isMatch = await bcrypt.compare(password, user.password);
		if (!isMatch)
			return res.status(400).json({ message: 'Invalid credentials' });

		// Generate JWT token
		const token = jwt.sign(
			{ id: user._id, isAdmin: user.isAdmin },
			process.env.JWT_SECRET,
			{ expiresIn: '30d' },
		);

		// Save the token in the database
		const expiresAt = new Date();
		expiresAt.setDate(expiresAt.getDate() + 30); // 30-day expiration

		await Token.create({
			userId: user._id,
			token,
			type: 'access',
			device: req.headers['user-agent'] || 'unknown',
			expiresAt,
		});

		res.json({
			token,
			user: {
				id: user._id,
				name: user.name,
				isAdmin: user.isAdmin,
				superAdmin: user.superAdmin,
				documentToken: user.documentToken,
			},
		});
	} catch (error) {
		console.error('Login Error:', error);
		res.status(500).json({ message: 'Server Error' });
	}
};
// Verify OTP and Reset Password
const resetPassword = async (req, res) => {
	const { email, otp, newPassword } = req.body;

	try {
		// Check if user exists
		const user = await User.findOne({ email });
		if (!user) {
			return res.status(404).json({ message: 'User not found' });
		}

		// Verify OTP
		await verifyOTP(user._id, otp);

		// Hash the new password
		const hashedPassword = await bcrypt.hash(newPassword, 10);

		// Update user's password
		await User.findByIdAndUpdate(user._id, { password: hashedPassword });

		res
			.status(200)
			.json({ success: true, message: 'Password reset successfully' });
	} catch (error) {
		console.error('Reset Password Error:', error);
		res.status(400).json({ message: error.message });
	}
};

const forgotPassword = async (req, res) => {
	const { email } = req.body;

	try {
		// Check if user exists
		const user = await User.findOne({ email });
		if (!user) {
			return res.status(404).json({ message: 'User not found' });
		}

		// Generate and save OTP
		const otp = generateOTP();
		await saveOTP(user._id, otp);

		// Send OTP via email
		await sendEmail(
			email,
			'Password Reset OTP',
			`Your OTP for password reset is: ${otp}. This code expires in 5 minutes.`,
		);

		res.status(200).json({
			success: true,
			message: 'OTP sent successfully',
		});
	} catch (error) {
		console.error('Forgot Password Error:', error);
		res.status(500).json({ message: 'Server Error' });
	}
};
const checkUserStatus = async (req, res) => {
	const authHeader = req.headers.authorization;

	if (!authHeader || !authHeader.startsWith('Bearer ')) {
		return res
			.status(401)
			.json({ message: 'Unauthorized - No token provided' });
	}

	const token = authHeader.split(' ')[1];

	try {
		// Verify JWT
		const decoded = jwt.verify(token, process.env.JWT_SECRET);

		// Check if token exists in database
		const tokenExists = await Token.findOne({
			userId: decoded.id,
			token: token,
			expiresAt: { $gt: new Date() },
		});

		if (!tokenExists) {
			return res.status(401).json({ message: 'Unauthorized - Invalid token' });
		}

		// Get user data
		const user = await User.findById(decoded.id).select(
			'-password -__v -createdAt -updatedAt',
		);

		if (!user) {
			return res.status(404).json({ message: 'User not found' });
		}

		// Return updated user status
		res.json({
			user: {
				id: user._id,
				name: user.name,
				email: user.email,
				isAdmin: user.isAdmin,
				superAdmin: user.superAdmin,
				documentToken: user.documentToken,
				documentsReceived: user.documentsReceived,
				isVerified: user.isVerified,
				matricNumber: user.matricNumber,
			},
		});
	} catch (error) {
		console.error('Status check error:', error);

		if (error.name === 'TokenExpiredError') {
			return res.status(401).json({ message: 'Token expired' });
		}

		if (error.name === 'JsonWebTokenError') {
			return res.status(401).json({ message: 'Invalid token' });
		}

		res.status(500).json({ message: 'Server error' });
	}
};

// Google Authentication Routes
const googleLogin = passport.authenticate('google', {
	scope: ['profile', 'email'],
});

const googleCallback = (req, res, next) => {
	passport.authenticate('google', async (err, user, info) => {
		if (err || !user) {
			console.error('Google authentication error:', err || 'No user returned');
			return res.redirect(
				`${process.env.FRONTEND_URL}/login?error=google_auth_failed`,
			);
		}

		req.logIn(user, async (err) => {
			if (err) {
				console.error('Session establishment error:', err);
				return next(err);
			}

			// Generate JWT token
			const token = jwt.sign(
				{ id: user._id, isAdmin: user.isAdmin },
				process.env.JWT_SECRET,
				{ expiresIn: '30d' },
			);

			// Save the token in the database
			await Token.create({
				userId: user._id,
				token,
				type: 'access',
				device: req.headers['user-agent'] || 'unknown',
				expiresAt: new Date(Date.now() + 720 * 60 * 60 * 1000), // 30-day expiration
			});

			// Redirect to frontend with token
			res.redirect(
				`${process.env.FRONTEND_URL}/google-auth-callback?token=${token}`,
			);
		});
	})(req, res, next);
};
const getUser = async (req, res) => {
	try {
		const token = req.headers.authorization?.split(' ')[1];
		if (!token) return res.status(401).json({ message: 'Unauthorized' });

		const decoded = jwt.verify(token, process.env.JWT_SECRET);
		const user = await User.findById(decoded.id).select('-password'); // Exclude password

		if (!user) return res.status(404).json({ message: 'User not found' });

		res.json(user);
		console.log(User);
	} catch (err) {
		console.error(err);
		res.status(401).json({ message: 'Invalid token' });
	}
};

module.exports = {
	registerUser,
	loginUser,
	verifyEmail,
	resetPassword,
	forgotPassword,
	checkUserStatus,
	googleLogin,
	googleCallback,
	getUser,
};
