const Subscription = require('../models/NotificationSubs');
const webpush = require('web-push');

// Initialize VAPID keys
const vapidKeys = {
	publicKey: process.env.PUBLIC_VAPID_KEY,
	privateKey: process.env.PRIVATE_VAPID_KEY,
};

webpush.setVapidDetails(
	'mailto:<EMAIL>',
	vapidKeys.publicKey,
	vapidKeys.privateKey,
);

// Helper function to fix FCM endpoints if needed
const fixFcmEndpoint = (endpoint) => {
	// Check if this is an FCM endpoint that needs fixing
	if (endpoint.includes('fcm.googleapis.com/fcm/send/')) {
		// Extract the token part after the last slash
		const token = endpoint.split('/').pop();
		// Return the corrected endpoint format
		return `https://fcm.googleapis.com/wp/${token}`;
	}
	return endpoint;
};

const subscribe = async (req, res) => {
	const { uid, subscription } = req.body;

	// Validate the payload
	if (!uid || !subscription) {
		return res.status(400).json({ error: 'UID and subscription are required' });
	}

	// Validate the subscription object
	if (
		!subscription.endpoint ||
		!subscription.keys ||
		!subscription.keys.p256dh ||
		!subscription.keys.auth
	) {
		return res.status(400).json({ error: 'Invalid subscription object' });
	}

	try {
		// Fix the endpoint format if needed
		const fixedSubscription = {
			...subscription,
			endpoint: fixFcmEndpoint(subscription.endpoint),
		};

		let subscriptionDoc = await Subscription.findOne({ uid });

		if (subscriptionDoc) {
			// Check if subscription already exists with either the original or fixed endpoint
			const existingSubscription = subscriptionDoc.subscriptions.find(
				(sub) =>
					sub.endpoint === fixedSubscription.endpoint ||
					sub.endpoint === subscription.endpoint,
			);

			if (!existingSubscription) {
				subscriptionDoc.subscriptions.push(fixedSubscription);
				subscriptionDoc.updatedAt = new Date();
				await subscriptionDoc.save();
			}
		} else {
			subscriptionDoc = new Subscription({
				uid,
				subscriptions: [fixedSubscription],
			});
			await subscriptionDoc.save();
		}

		res.status(201).json({ message: 'Subscription saved successfully' });
	} catch (error) {
		console.error('Error saving subscription:', error);
		res.status(500).json({ error: 'Failed to save subscription' });
	}
};

const sendNotification = async (req, res) => {
	const { recipientUid, senderName, messageContent } = req.body;

	if (!recipientUid || !senderName || !messageContent) {
		return res.status(400).json({
			error: 'Recipient UID, sender name, and message content are required',
		});
	}

	try {
		const subscriptionDoc = await Subscription.findOne({ uid: recipientUid });

		if (!subscriptionDoc || subscriptionDoc.subscriptions.length === 0) {
			return res.status(404).json({ error: 'No subscriptions found' });
		}

		const payload = JSON.stringify({
			title: `You have a notification from ${senderName}`,
			body: messageContent,
		});

		const validSubscriptions = [];
		const invalidSubscriptions = [];
		const endpointsToFix = [];

		// First pass: try to send notifications with existing endpoints
		for (const subscription of subscriptionDoc.subscriptions) {
			try {
				await webpush.sendNotification(subscription, payload);
				validSubscriptions.push(subscription);
			} catch (error) {
				console.error('Error sending notification:', error);

				if (error.statusCode === 410) {
					// Gone - subscription is no longer valid
					invalidSubscriptions.push(subscription);
				} else if (
					error.statusCode === 404 &&
					subscription.endpoint.includes('fcm.googleapis.com/fcm/send/')
				) {
					// Endpoint format needs to be fixed
					endpointsToFix.push(subscription);
				} else {
					// Other errors - add to invalid list
					invalidSubscriptions.push(subscription);
				}
			}
		}

		// Second pass: try with fixed endpoints
		for (const subscription of endpointsToFix) {
			try {
				const fixedSubscription = {
					...subscription,
					endpoint: fixFcmEndpoint(subscription.endpoint),
				};

				await webpush.sendNotification(fixedSubscription, payload);

				// Update the subscription in the database with the fixed endpoint
				await Subscription.updateOne(
					{
						uid: recipientUid,
						'subscriptions.endpoint': subscription.endpoint,
					},
					{
						$set: {
							'subscriptions.$.endpoint': fixedSubscription.endpoint,
						},
					},
				);

				validSubscriptions.push(fixedSubscription);
			} catch (error) {
				console.error('Error sending notification with fixed endpoint:', error);
				invalidSubscriptions.push(subscription);
			}
		}

		// Remove invalid subscriptions
		if (invalidSubscriptions.length > 0) {
			await Subscription.updateOne(
				{ uid: recipientUid },
				{
					$pull: {
						subscriptions: {
							endpoint: {
								$in: invalidSubscriptions.map((sub) => sub.endpoint),
							},
						},
					},
				},
			);
		}

		res.status(200).json({
			message: 'Notifications sent successfully',
			stats: {
				valid: validSubscriptions.length,
				fixed: endpointsToFix.length,
				invalid: invalidSubscriptions.length,
			},
		});
	} catch (error) {
		console.error('Error sending notifications:', error);
		res.status(500).json({ error: 'Failed to send notifications' });
	}
};

// Enhanced unsubscribe controller
const unsubscribe = async (req, res) => {
	const { uid, endpoint } = req.body;

	if (!uid || !endpoint) {
		return res.status(400).json({ error: 'UID and endpoint are required' });
	}

	try {
		// Find the subscription document
		const subDoc = await Subscription.findOne({ uid });

		if (!subDoc) {
			return res.status(404).json({ error: 'User not found' });
		}

		// Try both original and fixed endpoint formats
		const originalEndpoint = endpoint;
		const fixedEndpoint = fixFcmEndpoint(endpoint);

		// Remove the specific subscription (checking both formats)
		const initialCount = subDoc.subscriptions.length;
		subDoc.subscriptions = subDoc.subscriptions.filter(
			(sub) =>
				sub.endpoint !== originalEndpoint && sub.endpoint !== fixedEndpoint,
		);

		if (subDoc.subscriptions.length === initialCount) {
			return res.status(404).json({ error: 'Subscription not found' });
		}

		await subDoc.save();

		// If no subscriptions left, remove the document
		if (subDoc.subscriptions.length === 0) {
			await Subscription.deleteOne({ _id: subDoc._id });
		}

		res.status(200).json({ message: 'Unsubscribed successfully' });
	} catch (error) {
		console.error('Error unsubscribing:', error);
		res.status(500).json({ error: 'Failed to unsubscribe' });
	}
};

module.exports = { subscribe, sendNotification, unsubscribe };
