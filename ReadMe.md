# Computer Science Project Submission Platform

## Overview

This is a platform for students in the Computer Science department to submit their projects (PDFs) for printing. Admins can review submitted projects along with student details. The platform uses **Next.js** for the frontend and **Node.js, MongoDB, ImageKit, and JWT authentication** for the backend.

## Features

- **Student Registration & Authentication** (JWT-based)
- **Project Submission** (PDF upload to Cloudinary)
- **Admin Dashboard** (View submitted projects & manage admins)
- **Role-based Access Control**

## Tech Stack

- **Frontend**: Next.js, Tailwind CSS
- **Backend**: Node.js (Express)
- **Database**: MongoDB
- **Storage**: ImageKit (for PDF files)
- **Authentication**: JWT

## Setup Instructions

### Prerequisites

- Node.js installed
- MongoDB database
- ImageKit account for file storage

### Installation

1. Clone the repository:

   ```sh
   git clone https://github.com/DanielAgbeni/UploadDoc-backend.git
   cd your-repo-name
   ```

2. Install backend dependencies:

   ```sh
   cd backend
   npm install
   ```

3. Create a `.env` file in the `backend/` directory and add:

   ```env
   MONGO_URI=your_mongodb_connection_string
   JWT_SECRET=your_jwt_secret_key
   IMAGEKIT_PUBLIC_KEY=your_public_key
   IMAGEKIT_PRIVATE_KEY=your_private_key
   IMAGEKIT_URL_ENDPOINT=your_url_endpoint
   ```

4. Start the backend server:

   ```sh
   npm start
   ```

5. Install frontend dependencies:

   ```sh
   cd ../frontend
   npm install
   ```

6. Start the frontend:

   ```sh
   npm run dev
   ```

## API Endpoints

### Authentication

- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login and get JWT token

### Project Submission

- `POST /api/projects/upload` - Submit a project (Authenticated users)
- `GET /api/projects/all` - Get all submitted projects (Admins only)

## License

---
